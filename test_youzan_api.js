/**
 * 有赞API测试脚本
 * 测试完整的wxid -> code -> KDTWEAPPSESSIONID -> API调用流程
 */

const YouZanApiClient = require('./youzan_api_client');

class YouZanApiTester {
    constructor() {
        this.testResults = [];
    }

    /**
     * 测试单个wxid的完整流程
     */
    async testWxidFlow(wxid, label = null) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`🧪 测试wxid: ${wxid}`);
        console.log(`${'='.repeat(60)}`);
        
        const client = new YouZanApiClient({
            userLabel: label || wxid,
            debug: true
        });
        
        const testResult = {
            wxid: wxid,
            label: label,
            success: false,
            steps: {},
            authInfo: null,
            apiResults: {},
            error: null
        };
        
        try {
            // 步骤1: 初始化认证
            console.log('\n📋 步骤1: 初始化认证');
            const initSuccess = await client.initWithWxid(wxid);
            testResult.steps.init = initSuccess;
            
            if (!initSuccess) {
                throw new Error('认证初始化失败');
            }
            
            // 步骤2: 检查认证状态
            console.log('\n📋 步骤2: 检查认证状态');
            const authStatus = client.getAuthStatus();
            testResult.authInfo = authStatus;
            testResult.steps.authCheck = authStatus.isReady;
            
            console.log('认证状态:', JSON.stringify(authStatus, null, 2));
            
            if (!authStatus.isReady) {
                throw new Error('认证状态不完整');
            }
            
            // 步骤3: 测试API连接
            console.log('\n📋 步骤3: 测试API连接');
            const connectionTest = await client.testConnection();
            testResult.steps.connection = connectionTest;
            
            // 步骤4: 查询积分
            console.log('\n📋 步骤4: 查询积分');
            const points = await client.getPoints();
            testResult.apiResults.points = points;
            testResult.steps.getPoints = points >= 0;
            
            // 步骤5: 获取用户信息
            console.log('\n📋 步骤5: 获取用户信息');
            const userInfo = await client.getUserInfo();
            testResult.apiResults.userInfo = userInfo;
            testResult.steps.getUserInfo = !!userInfo;
            
            // 判断整体成功
            testResult.success = Object.values(testResult.steps).every(step => step === true);
            
            console.log(`\n✅ 测试完成 - ${testResult.success ? '成功' : '失败'}`);
            
        } catch (error) {
            testResult.error = error.message;
            console.log(`\n❌ 测试失败: ${error.message}`);
        }
        
        this.testResults.push(testResult);
        return testResult;
    }

    /**
     * 测试单个userId的完整流程
     */
    async testUserIdFlow(userId, label = null) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`🧪 测试userId: ${userId}`);
        console.log(`${'='.repeat(60)}`);
        
        const client = new YouZanApiClient({
            userLabel: label || `用户${userId}`,
            debug: true
        });
        
        const testResult = {
            userId: userId,
            label: label,
            success: false,
            steps: {},
            authInfo: null,
            apiResults: {},
            error: null
        };
        
        try {
            // 步骤1: 初始化认证
            console.log('\n📋 步骤1: 初始化认证');
            const initSuccess = await client.initWithUserId(userId);
            testResult.steps.init = initSuccess;
            
            if (!initSuccess) {
                throw new Error('认证初始化失败');
            }
            
            // 步骤2: 检查认证状态
            console.log('\n📋 步骤2: 检查认证状态');
            const authStatus = client.getAuthStatus();
            testResult.authInfo = authStatus;
            testResult.steps.authCheck = authStatus.isReady;
            
            console.log('认证状态:', JSON.stringify(authStatus, null, 2));
            
            // 步骤3: 查询积分
            console.log('\n📋 步骤3: 查询积分');
            const points = await client.getPoints();
            testResult.apiResults.points = points;
            testResult.steps.getPoints = points >= 0;
            
            // 判断整体成功
            testResult.success = Object.values(testResult.steps).every(step => step === true);
            
            console.log(`\n✅ 测试完成 - ${testResult.success ? '成功' : '失败'}`);
            
        } catch (error) {
            testResult.error = error.message;
            console.log(`\n❌ 测试失败: ${error.message}`);
        }
        
        this.testResults.push(testResult);
        return testResult;
    }

    /**
     * 批量测试多个账号
     */
    async batchTest() {
        console.log('🚀 开始批量测试有赞API调用流程');
        console.log('='.repeat(80));
        
        // 测试案例
        const testCases = [
            { type: 'wxid', value: 'wxid_ptziv4e765dy22', label: '测试账号1' },
            { type: 'userId', value: 2853813, label: '测试账号2' },
            { type: 'userId', value: 2854685, label: '测试账号3' },
            { type: 'wxid', value: 'wxid_test123456', label: '测试账号4' }
        ];
        
        for (const testCase of testCases) {
            try {
                if (testCase.type === 'wxid') {
                    await this.testWxidFlow(testCase.value, testCase.label);
                } else {
                    await this.testUserIdFlow(testCase.value, testCase.label);
                }
                
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.log(`❌ 测试案例失败: ${testCase.value} - ${error.message}`);
            }
        }
        
        // 生成测试报告
        this.generateTestReport();
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📊 测试报告');
        console.log('='.repeat(80));
        
        const summary = {
            totalTests: this.testResults.length,
            successfulTests: this.testResults.filter(r => r.success).length,
            failedTests: this.testResults.filter(r => !r.success).length,
            wxidTests: this.testResults.filter(r => r.wxid).length,
            userIdTests: this.testResults.filter(r => r.userId).length
        };
        
        console.log('📈 测试统计:');
        Object.entries(summary).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
        
        console.log('\n📋 详细结果:');
        this.testResults.forEach((result, index) => {
            const identifier = result.wxid || `userId:${result.userId}`;
            const status = result.success ? '✅ 成功' : '❌ 失败';
            console.log(`   ${index + 1}. ${identifier} - ${status}`);
            
            if (result.error) {
                console.log(`      错误: ${result.error}`);
            }
            
            if (result.apiResults.points !== undefined) {
                console.log(`      积分: ${result.apiResults.points}`);
            }
        });
        
        // 保存详细报告
        const fs = require('fs');
        const reportData = {
            summary: summary,
            testResults: this.testResults,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync('youzan_api_test_report.json', JSON.stringify(reportData, null, 2));
        console.log('\n📄 详细报告已保存到: youzan_api_test_report.json');
        
        // 成功率统计
        const successRate = (summary.successfulTests / summary.totalTests * 100).toFixed(1);
        console.log(`\n🎯 总体成功率: ${successRate}%`);
    }

    /**
     * 快速测试单个账号
     */
    async quickTest(identifier) {
        console.log(`🚀 快速测试: ${identifier}`);
        
        if (identifier.startsWith('wxid_')) {
            return await this.testWxidFlow(identifier);
        } else if (!isNaN(identifier)) {
            return await this.testUserIdFlow(parseInt(identifier));
        } else {
            console.log('❌ 无效的标识符，请使用wxid或userId');
            return null;
        }
    }
}

// 命令行工具
if (require.main === module) {
    const tester = new YouZanApiTester();
    const args = process.argv.slice(2);
    const command = args[0];

    if (!command) {
        console.log(`
有赞API测试工具

使用方法:
  node test_youzan_api.js <command> [参数]

命令:
  batch                    - 批量测试所有账号
  wxid <wxid>             - 测试指定wxid
  userid <userid>         - 测试指定userId
  quick <wxid|userid>     - 快速测试单个账号

示例:
  node test_youzan_api.js batch
  node test_youzan_api.js wxid wxid_ptziv4e765dy22
  node test_youzan_api.js userid 2853813
  node test_youzan_api.js quick wxid_ptziv4e765dy22
        `);
        process.exit(0);
    }

    (async () => {
        try {
            switch (command) {
                case 'batch':
                    await tester.batchTest();
                    break;

                case 'wxid':
                    const wxid = args[1];
                    if (!wxid) {
                        console.error('❌ 请提供wxid参数');
                        process.exit(1);
                    }
                    await tester.testWxidFlow(wxid);
                    break;

                case 'userid':
                    const userId = parseInt(args[1]);
                    if (!userId) {
                        console.error('❌ 请提供有效的userId参数');
                        process.exit(1);
                    }
                    await tester.testUserIdFlow(userId);
                    break;

                case 'quick':
                    const identifier = args[1];
                    if (!identifier) {
                        console.error('❌ 请提供wxid或userId参数');
                        process.exit(1);
                    }
                    await tester.quickTest(identifier);
                    break;

                default:
                    console.error(`❌ 未知命令: ${command}`);
                    process.exit(1);
            }
        } catch (error) {
            console.error('❌ 执行失败:', error.message);
            process.exit(1);
        }
    })();
}

module.exports = YouZanApiTester;
