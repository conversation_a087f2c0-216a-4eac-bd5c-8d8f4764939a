/**
 * 有赞API客户端
 * 集成微信code生成和KDTWEAPPSESSIONID生成，实现完整的API调用流程
 */

const axios = require('axios');
const { AdvancedWeChatSimulator } = require('./wechat_code_simulator');
const KDTWEAPPSESSIONIDGenerator = require('./kdtweappsessionid_generator');

class YouZanApiClient {
    constructor(options = {}) {
        this.kdt_id = options.kdt_id || '46308965';
        this.appId = options.appId || 'wxddc38f2f387306b2';
        this.userLabel = options.userLabel || '用户';
        this.debug = options.debug || false;
        
        // 初始化生成器
        this.wxSimulator = new AdvancedWeChatSimulator({ debug: this.debug });
        this.sessionGenerator = new KDTWEAPPSESSIONIDGenerator();
        
        // 用户认证信息
        this.authInfo = {
            wxid: null,
            userId: null,
            code: null,
            sessionKey: null,
            openId: null,
            kdtSessionId: null,
            accessToken: null
        };
    }

    /**
     * 使用wxid初始化用户认证
     */
    async initWithWxid(wxid) {
        try {
            this.log(`🔐 开始为wxid初始化认证: ${wxid}`);
            
            // 1. 生成微信认证信息
            const authResult = await this.wxSimulator.simulateWxMiniProgramAuth(wxid);
            
            this.authInfo.wxid = wxid;
            this.authInfo.code = authResult.code;
            this.authInfo.sessionKey = authResult.sessionKey;
            this.authInfo.openId = authResult.openId;
            
            this.log(`✅ 微信认证完成`);
            this.debug && console.log('   认证信息:', {
                code: this.authInfo.code,
                openId: this.authInfo.openId,
                sessionKey: this.authInfo.sessionKey
            });
            
            // 2. 生成KDTWEAPPSESSIONID
            await this.generateKDTSession();
            
            // 3. 获取access_token
            await this.getAccessToken();
            
            this.log(`🎉 用户认证初始化完成`);
            return true;
            
        } catch (error) {
            this.log(`❌ 认证初始化失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 使用userId初始化用户认证
     */
    async initWithUserId(userId) {
        try {
            this.log(`🔐 开始为userId初始化认证: ${userId}`);
            
            // 1. 生成微信认证信息
            const authResult = await this.wxSimulator.simulateWxMiniProgramAuth(null, userId);
            
            this.authInfo.userId = userId;
            this.authInfo.code = authResult.code;
            this.authInfo.sessionKey = authResult.sessionKey;
            this.authInfo.openId = authResult.openId;
            
            this.log(`✅ 微信认证完成`);
            
            // 2. 生成KDTWEAPPSESSIONID
            await this.generateKDTSession();
            
            // 3. 获取access_token
            await this.getAccessToken();
            
            this.log(`🎉 用户认证初始化完成`);
            return true;
            
        } catch (error) {
            this.log(`❌ 认证初始化失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 生成KDTWEAPPSESSIONID
     */
    async generateKDTSession() {
        try {
            this.log(`🔑 生成KDTWEAPPSESSIONID...`);
            
            // 使用多种算法尝试生成
            const sessionParams = {
                wxCode: this.authInfo.code,
                appId: this.appId,
                openId: this.authInfo.openId,
                sessionKey: this.authInfo.sessionKey,
                kdtId: this.kdt_id
            };
            
            const sessionResult = this.sessionGenerator.generate(sessionParams);
            
            if (sessionResult) {
                this.authInfo.kdtSessionId = sessionResult.sessionId;
                this.log(`✅ KDTWEAPPSESSIONID生成成功: ${sessionResult.sessionId.substring(0, 16)}...`);
                this.debug && console.log('   完整SessionId:', sessionResult.sessionId);
                return true;
            } else {
                throw new Error('KDTWEAPPSESSIONID生成失败');
            }
            
        } catch (error) {
            this.log(`❌ KDTWEAPPSESSIONID生成失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 获取access_token
     */
    async getAccessToken() {
        try {
            this.log(`🎫 获取access_token...`);
            
            // 模拟access_token生成
            const tokenData = this.authInfo.code + this.appId + this.kdt_id;
            const crypto = require('crypto');
            this.authInfo.accessToken = crypto.createHash('md5').update(tokenData).digest('hex');
            
            this.log(`✅ access_token获取成功: ${this.authInfo.accessToken.substring(0, 16)}...`);
            return true;
            
        } catch (error) {
            this.log(`❌ access_token获取失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 构建API请求头
     */
    getApiHeaders() {
        const headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.33(0x18002129) NetType/WIFI Language/zh_CN',
            'Referer': `https://mp-isv.youzanyun.com/c/${this.kdt_id}/`,
            'Origin': 'https://mp-isv.youzanyun.com'
        };

        // 添加认证相关头部
        if (this.authInfo.kdtSessionId) {
            headers['Cookie'] = `KDTWEAPPSESSIONID=${this.authInfo.kdtSessionId};`;
        }

        if (this.authInfo.openId && this.authInfo.kdtSessionId) {
            headers['Extra-Data'] = JSON.stringify({
                is_weapp: 1,
                sid: this.authInfo.kdtSessionId,
                version: "1.0.0"
            });
        }

        return headers;
    }

    /**
     * 查询积分
     */
    async getPoints() {
        try {
            const url = `https://mp-isv.youzanyun.com/c/${this.kdt_id}/user/get/integral`;
            const pointsHeaders = this.getApiHeaders();

            this.debug && console.log(`【${this.userLabel}】发送积分查询请求`, { url, headers: pointsHeaders });

            const response = await axios.get(url, { headers: pointsHeaders });
            const result = response.data;

            this.debug && console.log(`【${this.userLabel}】积分查询响应`, result);

            if (result?.code === 1 && result?.data) {
                const points = result.data.integral || 0;
                this.log(`💰 当前积分余额: ${points}`);
                return points;
            } else {
                this.log(`查询积分失败：${result?.msg || '未知错误'}`);
                return 0;
            }
        } catch (error) {
            this.log(`查询积分请求失败：${error.message}`, 'ERROR');
            this.debug && console.log(`【${this.userLabel}】积分查询错误详情`, {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                headers: error.response?.headers
            });
            return 0;
        }
    }

    /**
     * 获取用户信息
     */
    async getUserInfo() {
        try {
            const url = `https://mp-isv.youzanyun.com/c/${this.kdt_id}/user/get/info`;
            const headers = this.getApiHeaders();

            this.debug && console.log(`【${this.userLabel}】发送用户信息查询请求`, { url });

            const response = await axios.get(url, { headers });
            const result = response.data;

            this.debug && console.log(`【${this.userLabel}】用户信息响应`, result);

            if (result?.code === 1 && result?.data) {
                const userInfo = result.data;
                this.log(`👤 用户信息: ${userInfo.nickname || '未知'}`);
                return userInfo;
            } else {
                this.log(`获取用户信息失败：${result?.msg || '未知错误'}`);
                return null;
            }
        } catch (error) {
            this.log(`获取用户信息请求失败：${error.message}`, 'ERROR');
            return null;
        }
    }

    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            this.log(`🔗 测试API连接...`);
            
            // 先尝试获取用户信息
            const userInfo = await this.getUserInfo();
            if (userInfo) {
                this.log(`✅ API连接测试成功`);
                return true;
            }
            
            // 再尝试获取积分
            const points = await this.getPoints();
            if (points >= 0) {
                this.log(`✅ API连接测试成功`);
                return true;
            }
            
            this.log(`❌ API连接测试失败`);
            return false;
            
        } catch (error) {
            this.log(`❌ API连接测试失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 获取当前认证状态
     */
    getAuthStatus() {
        return {
            wxid: this.authInfo.wxid,
            userId: this.authInfo.userId,
            hasCode: !!this.authInfo.code,
            hasSessionKey: !!this.authInfo.sessionKey,
            hasOpenId: !!this.authInfo.openId,
            hasKDTSession: !!this.authInfo.kdtSessionId,
            hasAccessToken: !!this.authInfo.accessToken,
            isReady: !!(this.authInfo.code && this.authInfo.kdtSessionId)
        };
    }

    /**
     * 日志输出
     */
    log(message, level = 'INFO') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] 【${this.userLabel}】`;
        
        switch (level) {
            case 'ERROR':
                console.error(`${prefix} ❌ ${message}`);
                break;
            case 'WARN':
                console.warn(`${prefix} ⚠️ ${message}`);
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }
}

module.exports = YouZanApiClient;
