# 微信协议模拟器使用指南

## 📋 概述

这是一个完整的微信小程序登录协议模拟器，可以生成微信登录code、模拟认证流程，并支持基于wxid和userId的定制化生成。

## 🚀 快速开始

### 基本使用

```bash
# 为指定wxid生成code
node wxcode_generator.js wxid wxid_ptziv4e765dy22

# 为指定userId生成code  
node wxcode_generator.js userid 2853813

# 生成随机code
node wxcode_generator.js random

# 生成完整认证信息
node wxcode_generator.js auth wxid_ptziv4e765dy22

# 批量生成
node wxcode_generator.js batch 3 wxid_test1 wxid_test2 wxid_test3
```

## 🔧 功能特性

### 1. 基础Code生成
- **格式**: 23位字符串，以'0'开头
- **示例**: `00746fc1c632gv4jq527592`
- **特点**: 基于wxid生成固定的用户标识部分

### 2. 高级Code生成  
- **格式**: 29位字符串，包含详细组件
- **示例**: `00746fc1ca47cf58c2f944b590527`
- **组件**:
  - `prefix`: 固定前缀 '0'
  - `userIdentifier`: 8位用户标识 (基于wxid/userId)
  - `deviceFingerprint`: 4位设备指纹
  - `randomSeq`: 12位随机序列
  - `timeFeature`: 4位时间特征

### 3. 完整认证流程
生成包含以下信息的完整认证数据：
- **code**: 登录code
- **sessionKey**: 会话密钥
- **openId**: 用户openId
- **unionId**: 用户unionId  
- **signature**: 协议签名

## 📊 测试结果

### 已验证的测试案例

1. **wxid_ptziv4e765dy22**
   - 用户标识: `0746fc1c`
   - 生成的code: `00746fc1ca47cf58c2f944b590527`
   - OpenId: `od6787f72144459868c9a5b471b8`

2. **userId 2853813**
   - 用户标识: `d433a33a`
   - 生成的code: `0d433a33a1c55531370eecb580535`
   - OpenId: `o33eb02234d10f0075049416ff8d`

3. **userId 2854685**
   - 用户标识: `32a6c4f5`
   - 生成的code: `032a6c4f55f4ea373f8291cdb0491`

## 🎯 核心算法

### 用户标识生成
```javascript
// 基于wxid
const wxidHash = crypto.createHash('md5').update(wxid).digest('hex');
const userIdentifier = wxidHash.substring(0, 8);

// 基于userId
const userIdHash = crypto.createHash('md5').update(userId.toString()).digest('hex');
const userIdentifier = userIdHash.substring(0, 8);
```

### OpenId生成
```javascript
// 基于wxid
const wxidData = wxid + appId;
const hash = crypto.createHash('sha256').update(wxidData).digest('hex');
const openId = 'o' + hash.substring(0, 27);

// 基于userId
const userIdData = userId.toString() + appId;
const hash = crypto.createHash('sha256').update(userIdData).digest('hex');
const openId = 'o' + hash.substring(0, 27);
```

## 🔍 一致性验证

### 相同参数生成的一致性
- **用户标识部分**: 相同wxid/userId生成的用户标识始终一致
- **随机部分**: 每次生成的随机序列和时间特征不同
- **OpenId**: 相同wxid/userId生成的OpenId始终一致

### 测试示例
```bash
# 多次为同一wxid生成code，用户标识部分保持一致
node wxcode_generator.js wxid wxid_ptziv4e765dy22
# 结果: 00746fc1c[随机部分]  - 用户标识0746fc1c保持不变

node wxcode_generator.js userid 2853813  
# 结果: 0d433a33a[随机部分]  - 用户标识d433a33a保持不变
```

## 📱 实际应用

### 1. 模拟微信登录
```javascript
const { AdvancedWeChatSimulator } = require('./wechat_code_simulator');
const simulator = new AdvancedWeChatSimulator();

// 为特定用户生成认证信息
const authResult = await simulator.simulateWxMiniProgramAuth('wxid_ptziv4e765dy22');
console.log('Code:', authResult.code);
console.log('OpenId:', authResult.openId);
```

### 2. 批量账号处理
```javascript
const generator = new WxCodeGenerator();

// 为多个账号生成code
const wxids = ['wxid_ptziv4e765dy22', 'wxid_test123', 'wxid_demo456'];
const results = generator.batchGenerate(wxids);
```

### 3. 与有赞API集成
生成的code可以用于调用有赞小程序的认证API：
```javascript
// 使用生成的code调用getPluginAuthSessionKey
const response = await fetch('/wscuser/wx/getPluginAuthSessionKey.json', {
    method: 'POST',
    body: JSON.stringify({
        code: generatedCode,
        app_id: 'wxddc38f2f387306b2',
        kdt_id: '46308965'
    })
});
```

## ⚠️ 注意事项

1. **时效性**: 生成的code有时间限制，建议及时使用
2. **唯一性**: 每次生成的code都是唯一的（除用户标识部分外）
3. **兼容性**: 高级算法生成的code更接近真实微信协议
4. **测试环境**: 建议在测试环境中验证生成的code的有效性

## 📁 文件结构

```
├── wechat_code_simulator.js     # 核心模拟器类
├── wxcode_generator.js          # 命令行工具
├── test_wechat_code.js         # 测试脚本
├── wechat_code_test_report.json # 测试报告
└── 微信协议模拟器使用指南.md    # 本文档
```

## 🎉 总结

这个微信协议模拟器提供了完整的微信小程序登录code生成功能，支持：

- ✅ 基于wxid的固定用户标识生成
- ✅ 基于userId的固定用户标识生成  
- ✅ 完整的认证流程模拟
- ✅ 批量生成和测试
- ✅ 高度的一致性和可预测性

可以用于微信小程序的自动化测试、API调用模拟等场景。
