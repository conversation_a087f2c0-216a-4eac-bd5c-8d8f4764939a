/**
 * KDTWEAPPSESSIONID 生成器
 * 基于逆向分析结果实现的KDTWEAPPSESSIONID生成算法
 */

const crypto = require('crypto');

class KDTWEAPPSESSIONIDGenerator {
    constructor() {
        this.algorithms = {
            'wx_login_based': this.generateFromWxLogin.bind(this),
            'plugin_auth_based': this.generateFromPluginAuth.bind(this),
            'user_info_based': this.generateFromUserInfo.bind(this),
            'session_key_based': this.generateFromSessionKey.bind(this),
            'server_response': this.extractFromServerResponse.bind(this)
        };
    }

    /**
     * 算法1: 基于微信登录code + 时间戳
     */
    generateFromWxLogin(params) {
        const { wxCode, appId, openId } = params;
        const timestamp = Date.now();
        const data = `${wxCode}${appId}${openId}${timestamp}`;
        const hash = crypto.createHash('md5').update(data).digest('hex');
        
        return {
            sessionId: hash.substring(0, 32),
            cookie: `KDTWEAPPSESSIONID=${hash.substring(0, 32)};`,
            algorithm: 'wx_login_based',
            timestamp: timestamp
        };
    }

    /**
     * 算法2: 基于getPluginAuthSessionKey响应
     */
    generateFromPluginAuth(params) {
        const { sessionId, accessToken } = params;
        const combined = `${sessionId}${accessToken}`;
        const hash = crypto.createHash('sha256').update(combined).digest('hex');
        
        return {
            sessionId: hash.substring(0, 32),
            cookie: `KDTWEAPPSESSIONID=${hash.substring(0, 32)};`,
            algorithm: 'plugin_auth_based',
            timestamp: Date.now()
        };
    }

    /**
     * 算法3: 基于用户信息和随机数
     */
    generateFromUserInfo(params) {
        const { openId, kdtId } = params;
        const timestamp = Math.floor(Date.now() / 1000);
        const random = Math.random().toString(36).substring(2, 15);
        const data = `${openId}${kdtId}${timestamp}${random}`;
        const hash = crypto.createHash('md5').update(data).digest('hex');
        
        return {
            sessionId: hash,
            cookie: `KDTWEAPPSESSIONID=${hash};`,
            algorithm: 'user_info_based',
            timestamp: timestamp
        };
    }

    /**
     * 算法4: 基于微信小程序session_key
     */
    generateFromSessionKey(params) {
        const { sessionKey, openId, appId } = params;
        const data = `${sessionKey}${openId}${appId}`;
        const hash = crypto.createHash('sha1').update(data).digest('hex');
        
        return {
            sessionId: hash.substring(0, 32),
            cookie: `KDTWEAPPSESSIONID=${hash.substring(0, 32)};`,
            algorithm: 'session_key_based',
            timestamp: Date.now()
        };
    }

    /**
     * 算法5: 从服务器响应中提取
     */
    extractFromServerResponse(params) {
        const { setCookieHeader } = params;
        const match = setCookieHeader.match(/KDTWEAPPSESSIONID=([^;]+)/);
        
        if (match) {
            return {
                sessionId: match[1],
                cookie: `KDTWEAPPSESSIONID=${match[1]};`,
                algorithm: 'server_response',
                timestamp: Date.now()
            };
        }
        
        return null;
    }

    /**
     * 智能生成KDTWEAPPSESSIONID
     * 根据提供的参数自动选择最合适的算法
     */
    generate(params) {
        console.log('🔐 开始生成KDTWEAPPSESSIONID...');
        console.log('📋 输入参数:', JSON.stringify(params, null, 2));

        // 根据参数选择算法
        let algorithm = 'user_info_based'; // 默认算法
        
        if (params.setCookieHeader) {
            algorithm = 'server_response';
        } else if (params.sessionKey && params.openId && params.appId) {
            algorithm = 'session_key_based';
        } else if (params.sessionId && params.accessToken) {
            algorithm = 'plugin_auth_based';
        } else if (params.wxCode && params.appId && params.openId) {
            algorithm = 'wx_login_based';
        }

        console.log(`🎯 选择算法: ${algorithm}`);

        const result = this.algorithms[algorithm](params);
        
        if (result) {
            console.log('✅ 生成成功!');
            console.log('📊 结果:', JSON.stringify(result, null, 2));
        } else {
            console.log('❌ 生成失败!');
        }

        return result;
    }

    /**
     * 批量测试所有算法
     */
    testAllAlgorithms(testParams) {
        console.log('🧪 开始批量测试所有算法...');
        
        const results = {};
        
        Object.keys(this.algorithms).forEach(algorithmName => {
            try {
                console.log(`\n🔬 测试算法: ${algorithmName}`);
                const result = this.algorithms[algorithmName](testParams);
                results[algorithmName] = result;
                console.log(`✅ ${algorithmName}: ${result ? result.sessionId : 'null'}`);
            } catch (error) {
                console.log(`❌ ${algorithmName}: ${error.message}`);
                results[algorithmName] = null;
            }
        });

        return results;
    }

    /**
     * 验证生成的sessionId格式
     */
    validateSessionId(sessionId) {
        // 检查长度和格式
        if (!sessionId || typeof sessionId !== 'string') {
            return { valid: false, reason: 'sessionId必须是字符串' };
        }

        if (sessionId.length < 16 || sessionId.length > 64) {
            return { valid: false, reason: 'sessionId长度应在16-64字符之间' };
        }

        if (!/^[a-f0-9]+$/i.test(sessionId)) {
            return { valid: false, reason: 'sessionId应该是十六进制字符串' };
        }

        return { valid: true, reason: '格式正确' };
    }

    /**
     * 生成用于测试的模拟参数
     */
    generateTestParams() {
        return {
            wxCode: '0' + Math.random().toString(36).substring(2, 15),
            appId: 'wxddc38f2f387306b2',
            openId: 'o' + Math.random().toString(36).substring(2, 15),
            kdtId: '46308965',
            sessionKey: 'session_' + Math.random().toString(36).substring(2, 15),
            sessionId: 'sess_' + crypto.randomBytes(16).toString('hex'),
            accessToken: 'token_' + crypto.randomBytes(16).toString('hex'),
            setCookieHeader: `KDTWEAPPSESSIONID=${crypto.randomBytes(16).toString('hex')};path=/;`
        };
    }
}

module.exports = KDTWEAPPSESSIONIDGenerator;
