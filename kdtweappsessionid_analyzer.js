/**
 * KDTWEAPPSESSIONID 生成算法分析器
 * 专门分析有赞小程序中KDTWEAPPSESSIONID的生成逻辑
 */

const fs = require('fs');
const crypto = require('crypto');

class KDTWEAPPSESSIONIDAnalyzer {
    constructor() {
        this.findings = [];
        this.sessionPatterns = [];
        this.cookiePatterns = [];
    }

    // 分析KDTWEAPPSESSIONID相关的代码
    analyzeKDTWEAPPSESSIONID(filename, content) {
        console.log(`\n🔍 分析${filename}中的KDTWEAPPSESSIONID...`);
        
        // 查找KDTWEAPPSESSIONID相关的模式
        const patterns = [
            /KDTWEAPPSESSIONID[^;]{0,200}/g,
            /sessionId[^,;]{0,100}/g,
            /session[^,;]{0,100}/g,
            /cookie[^,;]{0,100}/g,
            /Set-Cookie[^,;]{0,100}/g,
            /getStorageSync\([^)]*token[^)]*\)/g,
            /setStorageSync\([^)]*session[^)]*\)/g
        ];

        patterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                console.log(`   📋 模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 120)}...`);
                });
                
                this.findings.push({
                    file: filename,
                    type: 'kdtweappsessionid',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 5)
                });
            }
        });
    }

    // 分析微信登录和session生成流程
    analyzeSessionGeneration(filename, content) {
        console.log(`\n🔐 分析${filename}中的Session生成流程...`);
        
        // 查找session生成相关的代码
        const sessionPatterns = [
            /wx\.login[^}]{0,300}/g,
            /getPluginAuthSessionKey[^}]{0,300}/g,
            /sessionKey[^,;]{0,100}/g,
            /session_id[^,;]{0,100}/g,
            /createSession[^}]{0,200}/g,
            /generateSession[^}]{0,200}/g
        ];

        sessionPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0 && matches.length < 20) {
                console.log(`   🔑 Session模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 150)}...`);
                });
                
                this.sessionPatterns.push({
                    file: filename,
                    type: 'session_generation',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 3)
                });
            }
        });
    }

    // 分析Cookie处理逻辑
    analyzeCookieHandling(filename, content) {
        console.log(`\n🍪 分析${filename}中的Cookie处理...`);
        
        // 查找cookie处理相关的代码
        const cookiePatterns = [
            /document\.cookie[^;]{0,100}/g,
            /getCookie[^}]{0,200}/g,
            /setCookie[^}]{0,200}/g,
            /parseCookie[^}]{0,200}/g,
            /Cookie[^,;]{0,100}/g,
            /headers\[['"]Cookie['"][^}]{0,100}/g,
            /Set-Cookie[^}]{0,200}/g
        ];

        cookiePatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0 && matches.length < 15) {
                console.log(`   🍪 Cookie模式${index + 1}: 找到${matches.length}个匹配`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 120)}...`);
                });
                
                this.cookiePatterns.push({
                    file: filename,
                    type: 'cookie_handling',
                    pattern: pattern.toString(),
                    matches: matches.slice(0, 3)
                });
            }
        });
    }

    // 查找加密和哈希算法
    findCryptoAlgorithms(filename, content) {
        console.log(`\n🔒 查找${filename}中的加密算法...`);
        
        // 查找可能用于生成sessionId的加密函数
        const cryptoPatterns = [
            /md5\([^)]+\)/g,
            /sha1\([^)]+\)/g,
            /sha256\([^)]+\)/g,
            /btoa\([^)]+\)/g,
            /atob\([^)]+\)/g,
            /crypto\.[^(]+\([^)]*\)/g,
            /Math\.random\(\)[^;]*/g,
            /Date\.now\(\)[^;]*/g,
            /timestamp[^,;]{0,50}/g,
            /uuid[^,;]{0,50}/g
        ];
        
        cryptoPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length < 10) {
                console.log(`   🔐 加密模式${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 3).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match}`);
                });
            }
        });
    }

    // 分析API调用中的认证参数
    analyzeAuthParams(filename, content) {
        console.log(`\n🌐 分析${filename}中的认证参数...`);
        
        // 查找API调用中的认证相关参数
        const authPatterns = [
            /headers\s*:\s*{[^}]*}/g,
            /Authorization[^,;]{0,100}/g,
            /Extra-Data[^}]{0,200}/g,
            /access_token[^,;]{0,100}/g,
            /app_id[^,;]{0,100}/g,
            /kdt_id[^,;]{0,100}/g,
            /store_id[^,;]{0,100}/g
        ];

        authPatterns.forEach((pattern, index) => {
            const matches = content.match(pattern);
            if (matches && matches.length > 0 && matches.length < 10) {
                console.log(`   🔑 认证参数${index + 1}: 找到${matches.length}个`);
                matches.slice(0, 2).forEach((match, i) => {
                    console.log(`      ${i + 1}: ${match.substring(0, 100)}...`);
                });
            }
        });
    }

    // 运行完整分析
    runFullAnalysis() {
        console.log('🚀 开始KDTWEAPPSESSIONID算法分析\n');
        
        const files = ['commons.js', 'app.js', 'c.js', 'v.js'];
        
        files.forEach(filename => {
            if (fs.existsSync(filename)) {
                console.log(`\n${'='.repeat(60)}`);
                console.log(`📁 分析文件: ${filename}`);
                console.log(`${'='.repeat(60)}`);
                
                const content = fs.readFileSync(filename, 'utf8');
                
                this.analyzeKDTWEAPPSESSIONID(filename, content);
                this.analyzeSessionGeneration(filename, content);
                this.analyzeCookieHandling(filename, content);
                this.findCryptoAlgorithms(filename, content);
                this.analyzeAuthParams(filename, content);
            }
        });
        
        // 生成分析报告
        this.generateAnalysisReport();
        
        return this.findings;
    }

    // 生成分析报告
    generateAnalysisReport() {
        console.log('\n📋 KDTWEAPPSESSIONID分析报告');
        console.log('='.repeat(60));
        
        // 统计发现的模式
        const typeStats = {};
        this.findings.forEach(finding => {
            typeStats[finding.type] = (typeStats[finding.type] || 0) + 1;
        });
        
        console.log('发现的模式类型:');
        Object.entries(typeStats).forEach(([type, count]) => {
            console.log(`  ${type}: ${count}个模式`);
        });
        
        // 关键发现
        console.log('\n🎯 关键发现:');
        
        // KDTWEAPPSESSIONID相关
        const kdtFindings = this.findings.filter(f => f.type === 'kdtweappsessionid');
        if (kdtFindings.length > 0) {
            console.log('\n🔑 KDTWEAPPSESSIONID相关:');
            kdtFindings.forEach(finding => {
                console.log(`  文件: ${finding.file}`);
                finding.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        // Session生成相关
        if (this.sessionPatterns.length > 0) {
            console.log('\n🔐 Session生成相关:');
            this.sessionPatterns.forEach(pattern => {
                console.log(`  文件: ${pattern.file}`);
                pattern.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        // Cookie处理相关
        if (this.cookiePatterns.length > 0) {
            console.log('\n🍪 Cookie处理相关:');
            this.cookiePatterns.forEach(pattern => {
                console.log(`  文件: ${pattern.file}`);
                pattern.matches.forEach(match => {
                    console.log(`    - ${match.substring(0, 100)}...`);
                });
            });
        }
        
        // 生成可能的算法
        this.generatePossibleAlgorithms();
        
        // 保存详细报告
        const report = {
            findings: this.findings,
            sessionPatterns: this.sessionPatterns,
            cookiePatterns: this.cookiePatterns,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync('kdtweappsessionid_analysis.json', JSON.stringify(report, null, 2));
        console.log('\n📄 详细报告已保存到: kdtweappsessionid_analysis.json');
    }

    // 生成可能的KDTWEAPPSESSIONID算法
    generatePossibleAlgorithms() {
        console.log('\n💡 生成可能的KDTWEAPPSESSIONID算法...');

        const algorithms = [
            {
                name: '算法1: 基于微信登录code + 时间戳',
                description: '使用微信登录code、appId、openId生成sessionId',
                code: `
function generateKDTWEAPPSESSIONID(wxCode, appId, openId) {
    const timestamp = Date.now();
    const data = wxCode + appId + openId + timestamp;
    const hash = crypto.createHash('md5').update(data).digest('hex');
    return 'KDTWEAPPSESSIONID=' + hash.substring(0, 32) + ';';
}
                `
            },
            {
                name: '算法2: 基于getPluginAuthSessionKey响应',
                description: '从getPluginAuthSessionKey API响应中提取sessionId',
                code: `
function generateKDTWEAPPSESSIONID(authResponse) {
    const { sessionId, accessToken } = authResponse;
    const combined = sessionId + accessToken;
    const hash = crypto.createHash('sha256').update(combined).digest('hex');
    return 'KDTWEAPPSESSIONID=' + hash.substring(0, 32) + ';';
}
                `
            },
            {
                name: '算法3: 基于用户信息和随机数',
                description: '使用openId、kdtId、随机数生成',
                code: `
function generateKDTWEAPPSESSIONID(openId, kdtId) {
    const timestamp = Math.floor(Date.now() / 1000);
    const random = Math.random().toString(36).substring(2, 15);
    const data = openId + kdtId + timestamp + random;
    const hash = crypto.createHash('md5').update(data).digest('hex');
    return 'KDTWEAPPSESSIONID=' + hash + ';';
}
                `
            },
            {
                name: '算法4: 基于微信小程序session_key',
                description: '使用微信小程序的session_key生成',
                code: `
function generateKDTWEAPPSESSIONID(sessionKey, openId, appId) {
    const data = sessionKey + openId + appId;
    const hash = crypto.createHash('sha1').update(data).digest('hex');
    return 'KDTWEAPPSESSIONID=' + hash.substring(0, 32) + ';';
}
                `
            },
            {
                name: '算法5: 基于服务器响应Set-Cookie',
                description: '直接从服务器响应的Set-Cookie头中提取',
                code: `
function extractKDTWEAPPSESSIONID(setCookieHeader) {
    const match = setCookieHeader.match(/KDTWEAPPSESSIONID=([^;]+)/);
    if (match) {
        return 'KDTWEAPPSESSIONID=' + match[1] + ';';
    }
    return null;
}
                `
            }
        ];

        algorithms.forEach((alg, i) => {
            console.log(`\n${alg.name}:`);
            console.log(`   描述: ${alg.description}`);
            console.log(`   代码: ${alg.code.trim()}`);
        });

        console.log('\n🎯 实现建议:');
        console.log('1. 重点关注getPluginAuthSessionKey API的调用和响应');
        console.log('2. 分析微信登录流程中的session_key处理');
        console.log('3. 查看服务器响应的Set-Cookie头');
        console.log('4. 测试不同算法生成的sessionId与真实值的匹配度');
        console.log('5. 关注sessionId的存储和使用方式');

        return algorithms;
    }
}

// 运行分析
if (require.main === module) {
    const analyzer = new KDTWEAPPSESSIONIDAnalyzer();
    analyzer.runFullAnalysis();
}

module.exports = KDTWEAPPSESSIONIDAnalyzer;
