/**
 * 微信Code生成器 - 命令行工具
 * 快速生成微信登录code的实用工具
 */

const { WeChatCodeSimulator, AdvancedWeChatSimulator } = require('./wechat_code_simulator');

class WxCodeGenerator {
    constructor() {
        this.basicSimulator = new WeChatCodeSimulator({ debug: false });
        this.advancedSimulator = new AdvancedWeChatSimulator({ debug: false });
    }

    /**
     * 为指定wxid生成code
     */
    generateForWxid(wxid) {
        console.log(`🎯 为wxid生成微信code: ${wxid}`);
        
        const basicResult = this.basicSimulator.generateWxCode(wxid);
        const advancedResult = this.advancedSimulator.generateRealisticWxCode(wxid);
        
        return {
            wxid: wxid,
            basicCode: basicResult.code,
            advancedCode: advancedResult.code,
            timestamp: Date.now(),
            userIdentifier: advancedResult.components.userIdentifier
        };
    }

    /**
     * 为指定userId生成code
     */
    generateForUserId(userId) {
        console.log(`🎯 为userId生成微信code: ${userId}`);
        
        const advancedResult = this.advancedSimulator.generateRealisticWxCode(null, userId);
        
        return {
            userId: userId,
            code: advancedResult.code,
            timestamp: Date.now(),
            userIdentifier: advancedResult.components.userIdentifier,
            components: advancedResult.components
        };
    }

    /**
     * 生成随机code
     */
    generateRandom() {
        console.log(`🎲 生成随机微信code`);
        
        const basicResult = this.basicSimulator.generateWxCode();
        const advancedResult = this.advancedSimulator.generateRealisticWxCode();
        
        return {
            basicCode: basicResult.code,
            advancedCode: advancedResult.code,
            timestamp: Date.now()
        };
    }

    /**
     * 完整认证流程
     */
    async generateFullAuth(wxid = null, userId = null) {
        console.log(`🔐 生成完整认证信息`);
        
        const authResult = await this.advancedSimulator.simulateWxMiniProgramAuth(wxid, userId);
        
        return {
            wxid: wxid,
            userId: userId,
            code: authResult.code,
            sessionKey: authResult.sessionKey,
            openId: authResult.openId,
            unionId: authResult.unionId,
            signature: authResult.signature,
            timestamp: authResult.timestamp
        };
    }

    /**
     * 批量生成
     */
    batchGenerate(wxids = [], userIds = [], count = 5) {
        console.log(`🔄 批量生成${count}个微信code`);
        
        const results = [];
        
        for (let i = 0; i < count; i++) {
            const wxid = wxids[i] || null;
            const userId = userIds[i] || null;
            
            let result;
            if (wxid) {
                result = this.generateForWxid(wxid);
            } else if (userId) {
                result = this.generateForUserId(userId);
            } else {
                result = this.generateRandom();
            }
            
            results.push({
                index: i + 1,
                ...result
            });
        }
        
        return results;
    }

    /**
     * 验证code格式
     */
    validateCode(code) {
        return this.basicSimulator.validateCode(code);
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        console.log(`
微信Code生成器 v1.0

使用方法:
  node wxcode_generator.js <command> [参数]

命令:
  wxid <wxid>              - 为指定wxid生成code
  userid <userid>          - 为指定userId生成code  
  random                   - 生成随机code
  auth <wxid> [userid]     - 生成完整认证信息
  batch <count> [wxids...] - 批量生成code
  validate <code>          - 验证code格式

示例:
  node wxcode_generator.js wxid wxid_ptziv4e765dy22
  node wxcode_generator.js userid 2853813
  node wxcode_generator.js random
  node wxcode_generator.js auth wxid_ptziv4e765dy22
  node wxcode_generator.js batch 3 wxid_test1 wxid_test2 wxid_test3
  node wxcode_generator.js validate 00746fc1c5f4edb29881ef60b0491

特殊用法:
  # 为已知的测试账号生成code
  node wxcode_generator.js wxid wxid_ptziv4e765dy22
  node wxcode_generator.js userid 2853813
  node wxcode_generator.js userid 2854685
        `);
    }
}

// 命令行处理
if (require.main === module) {
    const generator = new WxCodeGenerator();
    const args = process.argv.slice(2);
    const command = args[0];

    if (!command || command === 'help' || command === '--help' || command === '-h') {
        generator.showHelp();
        process.exit(0);
    }

    (async () => {
        try {
            let result;

            switch (command) {
                case 'wxid':
                    const wxid = args[1];
                    if (!wxid) {
                        console.error('❌ 请提供wxid参数');
                        process.exit(1);
                    }
                    result = generator.generateForWxid(wxid);
                    console.log('\n📊 生成结果:');
                    console.log(`   基础Code: ${result.basicCode}`);
                    console.log(`   高级Code: ${result.advancedCode}`);
                    console.log(`   用户标识: ${result.userIdentifier}`);
                    break;

                case 'userid':
                    const userId = parseInt(args[1]);
                    if (!userId) {
                        console.error('❌ 请提供有效的userId参数');
                        process.exit(1);
                    }
                    result = generator.generateForUserId(userId);
                    console.log('\n📊 生成结果:');
                    console.log(`   Code: ${result.code}`);
                    console.log(`   用户标识: ${result.userIdentifier}`);
                    console.log(`   组件: ${JSON.stringify(result.components, null, 2)}`);
                    break;

                case 'random':
                    result = generator.generateRandom();
                    console.log('\n📊 生成结果:');
                    console.log(`   基础Code: ${result.basicCode}`);
                    console.log(`   高级Code: ${result.advancedCode}`);
                    break;

                case 'auth':
                    const authWxid = args[1] || null;
                    const authUserId = args[2] ? parseInt(args[2]) : null;
                    result = await generator.generateFullAuth(authWxid, authUserId);
                    console.log('\n📊 认证结果:');
                    console.log(`   Code: ${result.code}`);
                    console.log(`   OpenId: ${result.openId}`);
                    console.log(`   SessionKey: ${result.sessionKey}`);
                    console.log(`   Signature: ${result.signature}`);
                    break;

                case 'batch':
                    const count = parseInt(args[1]) || 5;
                    const wxids = args.slice(2);
                    result = generator.batchGenerate(wxids, [], count);
                    console.log('\n📊 批量生成结果:');
                    result.forEach(item => {
                        const code = item.code || item.basicCode || item.advancedCode;
                        const identifier = item.wxid || item.userId || '随机';
                        console.log(`   ${item.index}. ${identifier}: ${code}`);
                    });
                    break;

                case 'validate':
                    const codeToValidate = args[1];
                    if (!codeToValidate) {
                        console.error('❌ 请提供要验证的code');
                        process.exit(1);
                    }
                    result = generator.validateCode(codeToValidate);
                    console.log('\n📊 验证结果:');
                    console.log(`   Code: ${codeToValidate}`);
                    console.log(`   有效: ${result.valid ? '✅ 是' : '❌ 否'}`);
                    console.log(`   原因: ${result.reason}`);
                    break;

                default:
                    console.error(`❌ 未知命令: ${command}`);
                    console.log('使用 "node wxcode_generator.js help" 查看帮助');
                    process.exit(1);
            }

        } catch (error) {
            console.error('❌ 执行失败:', error.message);
            process.exit(1);
        }
    })();
}

module.exports = WxCodeGenerator;
