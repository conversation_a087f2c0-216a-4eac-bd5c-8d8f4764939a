/**
 * 使用示例 - 微信协议模拟器和有赞API客户端
 * 演示如何使用wxid生成对应的KDTWEAPPSESSIONID并请求API
 */

const YouZanApiClient = require('./youzan_api_client');

async function example1_BasicUsage() {
    console.log('📋 示例1: 基本使用方法');
    console.log('='.repeat(50));
    
    // 创建API客户端
    const client = new YouZanApiClient({
        userLabel: '测试用户',
        debug: false  // 设置为true可以看到详细日志
    });
    
    // 使用wxid初始化
    const success = await client.initWithWxid('wxid_ptziv4e765dy22');
    
    if (success) {
        console.log('✅ 认证成功');
        
        // 查看认证状态
        const authStatus = client.getAuthStatus();
        console.log('认证状态:', authStatus);
        
        // 查询积分
        const points = await client.getPoints();
        console.log('积分查询结果:', points);
        
    } else {
        console.log('❌ 认证失败');
    }
}

async function example2_MultipleAccounts() {
    console.log('\n📋 示例2: 多账号处理');
    console.log('='.repeat(50));
    
    const accounts = [
        { type: 'wxid', value: 'wxid_ptziv4e765dy22', label: '账号1' },
        { type: 'userId', value: 2853813, label: '账号2' },
        { type: 'userId', value: 2854685, label: '账号3' }
    ];
    
    for (const account of accounts) {
        console.log(`\n处理${account.label}: ${account.value}`);
        
        const client = new YouZanApiClient({
            userLabel: account.label,
            debug: false
        });
        
        let success = false;
        if (account.type === 'wxid') {
            success = await client.initWithWxid(account.value);
        } else {
            success = await client.initWithUserId(account.value);
        }
        
        if (success) {
            const authStatus = client.getAuthStatus();
            console.log(`  ✅ 认证成功 - SessionId: ${authStatus.hasKDTSession ? '已生成' : '未生成'}`);
            
            // 可以在这里调用其他API
            // const points = await client.getPoints();
            
        } else {
            console.log(`  ❌ 认证失败`);
        }
        
        // 避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

async function example3_DetailedInfo() {
    console.log('\n📋 示例3: 详细信息展示');
    console.log('='.repeat(50));
    
    const client = new YouZanApiClient({
        userLabel: '详细示例',
        debug: true  // 开启详细日志
    });
    
    // 使用已知的测试账号
    const success = await client.initWithWxid('wxid_ptziv4e765dy22');
    
    if (success) {
        const authStatus = client.getAuthStatus();
        
        console.log('\n🔍 详细认证信息:');
        console.log(`  wxid: ${authStatus.wxid}`);
        console.log(`  hasCode: ${authStatus.hasCode}`);
        console.log(`  hasSessionKey: ${authStatus.hasSessionKey}`);
        console.log(`  hasOpenId: ${authStatus.hasOpenId}`);
        console.log(`  hasKDTSession: ${authStatus.hasKDTSession}`);
        console.log(`  isReady: ${authStatus.isReady}`);
        
        // 获取请求头信息
        const headers = client.getApiHeaders();
        console.log('\n📋 API请求头:');
        console.log(`  Cookie: ${headers.Cookie}`);
        console.log(`  Extra-Data: ${headers['Extra-Data']}`);
        console.log(`  User-Agent: ${headers['User-Agent'].substring(0, 50)}...`);
        
        // 测试API连接
        console.log('\n🔗 测试API连接...');
        const connectionTest = await client.testConnection();
        console.log(`连接测试结果: ${connectionTest ? '成功' : '失败'}`);
    }
}

async function example4_ErrorHandling() {
    console.log('\n📋 示例4: 错误处理');
    console.log('='.repeat(50));
    
    const client = new YouZanApiClient({
        userLabel: '错误处理示例',
        debug: false
    });
    
    try {
        // 尝试使用无效的wxid
        const success = await client.initWithWxid('invalid_wxid');
        
        if (!success) {
            console.log('❌ 预期的失败 - 无效wxid');
        }
        
        // 尝试在未认证的情况下调用API
        console.log('\n尝试在未认证状态下调用API...');
        const points = await client.getPoints();
        console.log(`积分查询结果: ${points}`);
        
    } catch (error) {
        console.log(`捕获到错误: ${error.message}`);
    }
}

async function example5_CustomConfiguration() {
    console.log('\n📋 示例5: 自定义配置');
    console.log('='.repeat(50));
    
    // 使用自定义配置
    const client = new YouZanApiClient({
        kdt_id: '46308965',  // 自定义店铺ID
        appId: 'wxddc38f2f387306b2',  // 自定义AppID
        userLabel: '自定义配置用户',
        debug: true
    });
    
    const success = await client.initWithUserId(2853813);
    
    if (success) {
        console.log('✅ 自定义配置认证成功');
        
        // 显示生成的认证信息
        const authStatus = client.getAuthStatus();
        console.log('认证状态:', authStatus);
    }
}

// 运行所有示例
async function runAllExamples() {
    console.log('🚀 微信协议模拟器和有赞API客户端使用示例');
    console.log('='.repeat(80));
    
    try {
        await example1_BasicUsage();
        await example2_MultipleAccounts();
        await example3_DetailedInfo();
        await example4_ErrorHandling();
        await example5_CustomConfiguration();
        
        console.log('\n🎉 所有示例运行完成!');
        
    } catch (error) {
        console.error('❌ 示例运行失败:', error.message);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const args = process.argv.slice(2);
    const exampleNumber = args[0];
    
    if (!exampleNumber) {
        console.log(`
使用示例选择器

使用方法:
  node example_usage.js [示例编号]

示例:
  node example_usage.js 1    - 基本使用方法
  node example_usage.js 2    - 多账号处理
  node example_usage.js 3    - 详细信息展示
  node example_usage.js 4    - 错误处理
  node example_usage.js 5    - 自定义配置
  node example_usage.js all  - 运行所有示例

或者直接运行:
  node example_usage.js
        `);
        
        // 默认运行示例1
        example1_BasicUsage();
    } else {
        switch (exampleNumber) {
            case '1':
                example1_BasicUsage();
                break;
            case '2':
                example2_MultipleAccounts();
                break;
            case '3':
                example3_DetailedInfo();
                break;
            case '4':
                example4_ErrorHandling();
                break;
            case '5':
                example5_CustomConfiguration();
                break;
            case 'all':
                runAllExamples();
                break;
            default:
                console.error(`❌ 未知示例编号: ${exampleNumber}`);
        }
    }
}

module.exports = {
    example1_BasicUsage,
    example2_MultipleAccounts,
    example3_DetailedInfo,
    example4_ErrorHandling,
    example5_CustomConfiguration,
    runAllExamples
};
