/**
 * 微信Code生成测试脚本
 * 测试各种微信登录code生成场景
 */

const { WeChatCodeSimulator, AdvancedWeChatSimulator } = require('./wechat_code_simulator');

class WeChatCodeTester {
    constructor() {
        this.basicSimulator = new WeChatCodeSimulator({ debug: true });
        this.advancedSimulator = new AdvancedWeChatSimulator({ debug: true });
        this.testResults = [];
    }

    /**
     * 测试基础code生成
     */
    testBasicCodeGeneration() {
        console.log('\n🧪 测试基础code生成...');
        console.log('='.repeat(50));
        
        const testCases = [
            { name: '随机生成', wxid: null },
            { name: '基于wxid_ptziv4e765dy22', wxid: 'wxid_ptziv4e765dy22' },
            { name: '基于测试wxid', wxid: 'wxid_test123456' },
            { name: '基于IAlCObK7753416985919893504', wxid: 'IAlCObK7753416985919893504' }
        ];

        testCases.forEach((testCase, index) => {
            console.log(`\n📋 测试案例 ${index + 1}: ${testCase.name}`);
            
            const result = this.basicSimulator.generateWxCode(testCase.wxid);
            const validation = this.basicSimulator.validateCode(result.code);
            
            this.testResults.push({
                type: 'basic',
                testCase: testCase.name,
                wxid: testCase.wxid,
                code: result.code,
                valid: validation.valid,
                timestamp: result.timestamp
            });
            
            console.log(`   ✅ Code: ${result.code}`);
            console.log(`   📏 长度: ${result.code.length}`);
            console.log(`   ✔️ 验证: ${validation.valid ? '通过' : '失败'}`);
        });
    }

    /**
     * 测试高级code生成
     */
    testAdvancedCodeGeneration() {
        console.log('\n🚀 测试高级code生成...');
        console.log('='.repeat(50));
        
        const testCases = [
            { name: '基于wxid_ptziv4e765dy22', wxid: 'wxid_ptziv4e765dy22', userId: null },
            { name: '基于userId 2853813', wxid: null, userId: 2853813 },
            { name: '基于userId 2854685', wxid: null, userId: 2854685 },
            { name: '同时基于wxid和userId', wxid: 'wxid_ptziv4e765dy22', userId: 2853813 }
        ];

        testCases.forEach((testCase, index) => {
            console.log(`\n📋 高级测试案例 ${index + 1}: ${testCase.name}`);
            
            const result = this.advancedSimulator.generateRealisticWxCode(testCase.wxid, testCase.userId);
            
            this.testResults.push({
                type: 'advanced',
                testCase: testCase.name,
                wxid: testCase.wxid,
                userId: testCase.userId,
                code: result.code,
                components: result.components,
                deviceFingerprint: result.deviceFingerprint,
                timestamp: result.timestamp
            });
            
            console.log(`   ✅ Code: ${result.code}`);
            console.log(`   📏 长度: ${result.code.length}`);
            console.log(`   🔧 组件:`, result.components);
        });
    }

    /**
     * 测试完整认证流程
     */
    async testFullAuthFlow() {
        console.log('\n🔐 测试完整认证流程...');
        console.log('='.repeat(50));
        
        const testCases = [
            { wxid: 'wxid_ptziv4e765dy22', userId: null },
            { wxid: null, userId: 2853813 }
        ];

        for (const testCase of testCases) {
            console.log(`\n📋 认证测试: wxid=${testCase.wxid}, userId=${testCase.userId}`);
            
            try {
                const authResult = await this.advancedSimulator.simulateWxMiniProgramAuth(
                    testCase.wxid, 
                    testCase.userId
                );
                
                this.testResults.push({
                    type: 'auth_flow',
                    wxid: testCase.wxid,
                    userId: testCase.userId,
                    authResult: authResult,
                    success: true
                });
                
                console.log(`   ✅ 认证成功`);
                console.log(`   🔑 Code: ${authResult.code}`);
                console.log(`   👤 OpenId: ${authResult.openId}`);
                console.log(`   🔐 SessionKey: ${authResult.sessionKey}`);
                
            } catch (error) {
                console.log(`   ❌ 认证失败: ${error.message}`);
                this.testResults.push({
                    type: 'auth_flow',
                    wxid: testCase.wxid,
                    userId: testCase.userId,
                    error: error.message,
                    success: false
                });
            }
        }
    }

    /**
     * 测试code一致性
     */
    testCodeConsistency() {
        console.log('\n🔄 测试code一致性...');
        console.log('='.repeat(50));
        
        const wxid = 'wxid_ptziv4e765dy22';
        const userId = 2853813;
        
        console.log(`📋 测试相同参数多次生成的一致性`);
        console.log(`   wxid: ${wxid}`);
        console.log(`   userId: ${userId}`);
        
        // 测试基于wxid的一致性
        console.log('\n🔍 基于wxid的一致性测试:');
        const wxidCodes = [];
        for (let i = 0; i < 3; i++) {
            const result = this.advancedSimulator.generateRealisticWxCode(wxid);
            wxidCodes.push(result.code);
            console.log(`   第${i + 1}次: ${result.code}`);
        }
        
        // 检查用户标识部分是否一致
        const userIdentifiers = wxidCodes.map(code => code.substring(1, 9));
        const isConsistent = userIdentifiers.every(id => id === userIdentifiers[0]);
        console.log(`   用户标识一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`   用户标识: ${userIdentifiers[0]}`);
        
        // 测试基于userId的一致性
        console.log('\n🔍 基于userId的一致性测试:');
        const userIdCodes = [];
        for (let i = 0; i < 3; i++) {
            const result = this.advancedSimulator.generateRealisticWxCode(null, userId);
            userIdCodes.push(result.code);
            console.log(`   第${i + 1}次: ${result.code}`);
        }
        
        const userIdIdentifiers = userIdCodes.map(code => code.substring(1, 9));
        const isUserIdConsistent = userIdIdentifiers.every(id => id === userIdIdentifiers[0]);
        console.log(`   用户标识一致性: ${isUserIdConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`   用户标识: ${userIdIdentifiers[0]}`);
    }

    /**
     * 测试特定场景
     */
    testSpecificScenarios() {
        console.log('\n🎯 测试特定场景...');
        console.log('='.repeat(50));
        
        // 场景1: 为已知的wxid生成code
        console.log('\n📋 场景1: 为已知wxid生成code');
        const knownWxid = 'wxid_ptziv4e765dy22';
        const wxidResult = this.advancedSimulator.generateCodeForWxid(knownWxid);
        console.log(`   Code: ${wxidResult.code}`);
        
        // 场景2: 为已知的userId生成code
        console.log('\n📋 场景2: 为已知userId生成code');
        const knownUserId = 2853813;
        const userIdResult = this.advancedSimulator.generateCodeForUserId(knownUserId);
        console.log(`   Code: ${userIdResult.code}`);
        
        // 场景3: 批量生成
        console.log('\n📋 场景3: 批量生成code');
        const batchWxids = ['wxid_ptziv4e765dy22', 'wxid_test123', 'wxid_demo456'];
        const batchResults = this.basicSimulator.batchGenerateCodes(3, batchWxids);
        batchResults.forEach(result => {
            console.log(`   ${result.wxid || '随机'}: ${result.code}`);
        });
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📊 测试报告');
        console.log('='.repeat(60));
        
        const summary = {
            totalTests: this.testResults.length,
            basicTests: this.testResults.filter(r => r.type === 'basic').length,
            advancedTests: this.testResults.filter(r => r.type === 'advanced').length,
            authTests: this.testResults.filter(r => r.type === 'auth_flow').length,
            successfulAuths: this.testResults.filter(r => r.type === 'auth_flow' && r.success).length
        };
        
        console.log('📈 测试统计:');
        Object.entries(summary).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
        
        console.log('\n🔍 生成的Code样例:');
        this.testResults.slice(0, 5).forEach((result, index) => {
            if (result.code) {
                console.log(`   ${index + 1}. ${result.code} (${result.type})`);
            }
        });
        
        // 保存详细报告
        const fs = require('fs');
        const reportData = {
            summary: summary,
            testResults: this.testResults,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync('wechat_code_test_report.json', JSON.stringify(reportData, null, 2));
        console.log('\n📄 详细报告已保存到: wechat_code_test_report.json');
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 开始微信Code生成测试');
        console.log('='.repeat(60));
        
        try {
            this.testBasicCodeGeneration();
            this.testAdvancedCodeGeneration();
            await this.testFullAuthFlow();
            this.testCodeConsistency();
            this.testSpecificScenarios();
            this.generateTestReport();
            
            console.log('\n🎉 所有测试完成!');
            
        } catch (error) {
            console.error('❌ 测试过程中出现错误:', error.message);
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new WeChatCodeTester();
    tester.runAllTests();
}

module.exports = WeChatCodeTester;
